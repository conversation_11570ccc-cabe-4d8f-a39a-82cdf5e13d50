services:
  backend:
    image: jefferyhjw/bilinote-backend:latest
    container_name: bilinote-backend
    restart: unless-stopped
    expose:
      - "8000"
    env_file:
      - .env

  frontend:
    image: jefferyhjw/bilinote-frontend:latest
    container_name: bilinote-frontend
    restart: unless-stopped
    expose:
      - "80"

  nginx:
    image: jefferyhjw/bilinote-nginx:latest
    container_name: bilinote-nginx
    ports:
      - "${APP_PORT}:80"
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
